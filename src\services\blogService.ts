import { blogPosts } from "@/data/blog-posts";

export interface BlogPost {
  id: string;
  title: string;
  date: string;
  author: string;
  category: string;
  description?: string;
  image: string;
  content: string;
  tags?: string[];
}

export interface Category {
  name: string;
  count: number;
}

export interface Tag {
  name: string;
  count: number;
}

// Convert the blog posts object to an array with IDs
export const getAllPosts = (): BlogPost[] => {
  return Object.entries(blogPosts).map(([id, post]) => ({
    id,
    title: post.title,
    date: post.date,
    author: post.author,
    category: post.category,
    description: post.description,
    image: post.image,
    content: post.content,
    // Extract tags from content or add manually
    tags: extractTagsFromPost(post)
  }));
};

// Get recent posts
export const getRecentPosts = (count: number = 3): { title: string; slug: string }[] => {
  const posts = getAllPosts();
  
  // Sort by date (newest first)
  const sortedPosts = posts.sort((a, b) => {
    return new Date(b.date).getTime() - new Date(a.date).getTime();
  });
  
  return sortedPosts.slice(0, count).map(post => ({
    title: post.title,
    slug: post.id
  }));
};

// Get all categories with post counts
export const getCategories = (): Category[] => {
  const posts = getAllPosts();
  const categoryMap = new Map<string, number>();
  
  posts.forEach(post => {
    const category = post.category;
    categoryMap.set(category, (categoryMap.get(category) || 0) + 1);
  });
  
  return Array.from(categoryMap.entries()).map(([name, count]) => ({
    name,
    count
  }));
};

// Get all tags with post counts
export const getTags = (): Tag[] => {
  const posts = getAllPosts();
  const tagMap = new Map<string, number>();
  
  posts.forEach(post => {
    const tags = post.tags || [];
    tags.forEach(tag => {
      tagMap.set(tag, (tagMap.get(tag) || 0) + 1);
    });
  });
  
  return Array.from(tagMap.entries()).map(([name, count]) => ({
    name,
    count
  }));
};

// Search posts by query
export const searchPosts = (query: string): BlogPost[] => {
  if (!query) return [];
  
  const posts = getAllPosts();
  const lowerQuery = query.toLowerCase();
  
  return posts.filter(post => 
    post.title.toLowerCase().includes(lowerQuery) ||
    post.content.toLowerCase().includes(lowerQuery) ||
    post.description?.toLowerCase().includes(lowerQuery) ||
    post.category.toLowerCase().includes(lowerQuery) ||
    post.author.toLowerCase().includes(lowerQuery) ||
    (post.tags || []).some(tag => tag.toLowerCase().includes(lowerQuery))
  );
};

// Helper function to extract tags from post content or add manually
function extractTagsFromPost(post: any): string[] {
  // For now, we'll manually assign tags based on categories and content
  const tags: string[] = [];
  
  // Add category as a tag
  tags.push(post.category);
  
  // Add some common tags based on content
  if (post.content.toLowerCase().includes('pdf')) tags.push('PDF');
  if (post.content.toLowerCase().includes('word')) tags.push('Word');
  if (post.content.toLowerCase().includes('convert')) tags.push('Conversion');
  if (post.content.toLowerCase().includes('compress')) tags.push('Compression');
  if (post.content.toLowerCase().includes('security')) tags.push('Security');
  if (post.content.toLowerCase().includes('password')) tags.push('Password Protection');
  
  // Return unique tags
  return Array.from(new Set(tags));
}
