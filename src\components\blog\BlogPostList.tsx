"use client";

import { useState, useEffect } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/New-UI/Accordion";
import { Alert, AlertDescription } from "@/components/New-UI/Alert";
import { Button } from "@/components/ui/button";
import Link from "next/link";

type BlogPost = {
  _id: string;
  title: string;
  slug: string;
  status: "draft" | "scheduled" | "published" | "archived";
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  scheduledAt?: string;
  authorId: {
    name: string;
    email: string;
  };
  tags: string[];
};

type PaginationInfo = {
  total: number;
  page: number;
  limit: number;
  pages: number;
};

type BlogPostListProps = {
  initialStatus?: "draft" | "scheduled" | "published" | "archived";
};

export default function BlogPostList({
  initialStatus = "published",
}: BlogPostListProps) {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 10,
    pages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);
  const [status, setStatus] = useState<string>(initialStatus);
  const [searchQuery, setSearchQuery] = useState("");

  const fetchPosts = async () => {
    setLoading(true);
    setError(null);
    try {
      const queryParams = new URLSearchParams();
      if (status) queryParams.set("status", status);
      if (searchQuery) queryParams.set("search", searchQuery);
      queryParams.set("page", pagination.page.toString());
      queryParams.set("limit", pagination.limit.toString());

      const response = await fetch(`/api/blog?${queryParams.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch posts");

      const data = await response.json();
      setPosts(data.posts);
      setPagination(data.pagination);
    } catch (err: any) {
      setError(err.message || "An error occurred while fetching posts");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts();
  }, [status, pagination.page, pagination.limit]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchPosts();
  };

  const handleStatusChange = (newStatus: string) => {
    setStatus(newStatus);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const handleSelectAll = () => {
    if (selectedPosts.length === posts.length) {
      setSelectedPosts([]);
    } else {
      setSelectedPosts(posts.map((post) => post._id));
    }
  };

  const handleSelectPost = (id: string) => {
    setSelectedPosts((prev) =>
      prev.includes(id)
        ? prev.filter((postId) => postId !== id)
        : [...prev, id],
    );
  };

  const handleDeleteSelected = async () => {
    if (
      !selectedPosts.length ||
      !confirm("Are you sure you want to delete the selected posts?")
    )
      return;

    setLoading(true);
    try {
      const results = await Promise.all(
        selectedPosts.map((id) =>
          fetch(`/api/blog/${id}`, { method: "DELETE" }).then((res) => ({
            id,
            success: res.ok,
          })),
        ),
      );

      const failures = results.filter((r) => !r.success);
      if (failures.length) {
        setError(`Failed to delete ${failures.length} posts`);
      }

      setSelectedPosts([]);
      fetchPosts();
    } catch (err: any) {
      setError(err.message || "An error occurred while deleting posts");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6 bg-white p-6 rounded-lg shadow-sm">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Blog Posts</h2>
        <Link href="/admin/blog/editor">
          <Button>New Post</Button>
        </Link>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex gap-2">
          <Button
            variant={status === "draft" ? "default" : "outline"}
            onClick={() => handleStatusChange("draft")}
          >
            Drafts
          </Button>
          <Button
            variant={status === "scheduled" ? "default" : "outline"}
            onClick={() => handleStatusChange("scheduled")}
          >
            Scheduled
          </Button>
          <Button
            variant={status === "published" ? "default" : "outline"}
            onClick={() => handleStatusChange("published")}
          >
            Published
          </Button>
          <Button
            variant={status === "archived" ? "default" : "outline"}
            onClick={() => handleStatusChange("archived")}
          >
            Archived
          </Button>
        </div>

        <form onSubmit={handleSearch} className="flex gap-2">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search posts..."
            className="px-3 py-2 border rounded-md"
          />
          <Button type="submit">Search</Button>
        </form>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {selectedPosts.length > 0 && (
        <div className="flex justify-between items-center bg-blue-50 p-3 rounded-md">
          <span>{selectedPosts.length} posts selected</span>
          <Button variant="destructive" onClick={handleDeleteSelected}>
            Delete Selected
          </Button>
        </div>
      )}

      {loading ? (
        <div className="text-center py-8">Loading...</div>
      ) : posts.length === 0 ? (
        <div className="text-center py-8">No posts found</div>
      ) : (
        <div className="border rounded-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={
                      selectedPosts.length === posts.length && posts.length > 0
                    }
                    onChange={handleSelectAll}
                    className="h-4 w-4"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Title
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Author
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Updated
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {posts.map((post) => (
                <tr key={post._id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedPosts.includes(post._id)}
                      onChange={() => handleSelectPost(post._id)}
                      className="h-4 w-4"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      {post.title}
                    </div>
                    <div className="text-sm text-gray-500">{post.slug}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        post.status === "published"
                          ? "bg-green-100 text-green-800"
                          : post.status === "draft"
                            ? "bg-gray-100 text-gray-800"
                            : post.status === "scheduled"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-red-100 text-red-800"
                      }`}
                    >
                      {post.status}
                    </span>
                    {post.status === "scheduled" && post.scheduledAt && (
                      <div className="text-xs text-gray-500 mt-1">
                        {formatDate(post.scheduledAt)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {post.authorId.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(post.updatedAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Link
                      href={`/admin/blog/edit/${post._id}`}
                      className="text-blue-600 hover:text-blue-900 mr-4"
                    >
                      Edit
                    </Link>
                    <Link
                      href={`/blog/${post.slug}`}
                      target="_blank"
                      className="text-green-600 hover:text-green-900"
                    >
                      View
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex justify-between items-center mt-4">
          <div>
            Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
            {pagination.total} results
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              disabled={pagination.page === 1}
              onClick={() =>
                setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
              }
            >
              Previous
            </Button>
            <Button
              variant="outline"
              disabled={pagination.page === pagination.pages}
              onClick={() =>
                setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
              }
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
