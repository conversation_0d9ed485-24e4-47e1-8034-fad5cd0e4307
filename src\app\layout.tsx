import type { Metada<PERSON> } from "next";
import "@/styles.css"; // Using @ alias for consistency
import Script from "next/script";
import { TempoInit } from "./tempo-init";
import { Providers } from "./providers";
import { ThemeInitializer } from "@/components/theme/ThemeInitializer";
import { MetaHead } from "@/components/MetaHead";
import { LoadingIndicator } from "@/components/ui/LoadingIndicator";
import { NavigationProgress } from "@/components/ui/nprogress";
import connectToDatabase from "@/lib/db";
import SiteSettings from "@/models/SiteSettings";
import { inter } from "@/lib/fonts"; // Import local font configuration

// Dynamic metadata generation
export async function generateMetadata(): Promise<Metadata> {
  try {
    // Connect to the database
    await connectToDatabase();

    // Find the site settings
    const settings = await SiteSettings.findOne({});

    // If no settings exist, return default metadata
    if (!settings) {
      return {
        title: {
          default: "PDF Tools - All-in-one PDF Solution",
          template: "%s | PDF Tools"
        },
        description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
        keywords: [
          "PDF tools",
          "convert PDF",
          "edit PDF",
          "merge PDF",
          "compress PDF"
        ],
        metadataBase: new URL('https://yourdomain.com'),
        openGraph: {
          title: "PDF Tools - All-in-one PDF Solution",
          description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
          url: "https://yourdomain.com",
          siteName: "PDF Tools",
          images: [
            {
              url: "/og-image.jpg",
              width: 1200,
              height: 630,
            }
          ],
          locale: "en_US",
          type: "website",
        },
        twitter: {
          card: "summary_large_image",
          title: "PDF Tools - All-in-one PDF Solution",
          description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
          images: ["/og-image.jpg"],
        },
      };
    }

    // Use the settings from the database
    const metadataBase = settings.siteUrl ? new URL(settings.siteUrl) : new URL('https://yourdomain.com');

    return {
      title: {
        default: settings.metaTitle || "PDF Tools",
        template: `%s | ${settings.siteName || "PDF Tools"}`
      },
      description: settings.metaDescription || "All-in-one PDF solution for your document needs",
      keywords: settings.metaKeywords || ["PDF tools", "convert PDF", "edit PDF"],
      metadataBase,
      openGraph: {
        title: settings.ogTitle || settings.metaTitle || "PDF Tools",
        description: settings.ogDescription || settings.metaDescription || "All-in-one PDF solution",
        url: settings.siteUrl || "https://yourdomain.com",
        siteName: settings.siteName || "PDF Tools",
        images: [
          {
            url: settings.ogImage || "/og-image.jpg",
            width: 1200,
            height: 630,
          }
        ],
        locale: "en_US",
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title: settings.ogTitle || settings.metaTitle || "PDF Tools",
        description: settings.ogDescription || settings.metaDescription || "All-in-one PDF solution",
        images: [settings.ogImage || "/og-image.jpg"],
        creator: settings.twitterHandle || "@pdftools",
      },
      icons: {
        icon: settings.faviconUrl || "/favicon.ico",
        shortcut: settings.faviconUrl || "/favicon.ico",
        apple: settings.faviconUrl || "/favicon.ico",
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);

    // Return default metadata if there's an error
    return {
      title: {
        default: "PDF Tools - All-in-one PDF Solution",
        template: "%s | PDF Tools"
      },
      description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
      keywords: ["PDF tools", "convert PDF", "edit PDF", "merge PDF", "compress PDF"],
      metadataBase: new URL('https://yourdomain.com'),
      openGraph: {
        title: "PDF Tools - All-in-one PDF Solution",
        description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
        url: "https://yourdomain.com",
        siteName: "PDF Tools",
        images: [{ url: "/og-image.jpg", width: 1200, height: 630 }],
        locale: "en_US",
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title: "PDF Tools - All-in-one PDF Solution",
        description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
        images: ["/og-image.jpg"],
      },
    };
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        <link rel="stylesheet" href="/fonts/fonts.css" />
      </head>
      <body>
        <Script
          src="https://api.tempo.new/proxy-asset?url=https://storage.googleapis.com/tempo-public-assets/error-handling.js"
          strategy="afterInteractive"
        />

        {/* Font loading verification script */}
        <Script id="font-verification" strategy="afterInteractive">{`
          // Verify local fonts loaded correctly
          if (typeof window !== 'undefined') {
            // Check if fonts are loaded
            document.fonts.ready.then(() => {
              console.log('All fonts loaded successfully');
            }).catch(err => {
              console.warn('Font loading issue detected, using system fallback');
              document.documentElement.classList.add('font-fallback');
            });
          }
        `}</Script>

        {/* Enhanced script to reset loading state and fix asset loading issues */}
        <Script id="reset-loading-state" strategy="afterInteractive">
          {`
            // Reset loading state when page loads
            if (typeof window !== 'undefined') {
              // Enhanced function to reset all loading states
              function resetAllLoadingStates() {
                console.log('Resetting all loading states');

                // Reset loading meta tag
                let loadingMeta = document.querySelector('meta[name="loading"]');
                if (loadingMeta) {
                  loadingMeta.setAttribute('content', 'false');
                }

                // Hide NProgress bar
                if (typeof NProgress !== 'undefined') {
                  NProgress.done();
                }

                // Reset any loading classes in the DOM
                document.querySelectorAll('.loading, .skeleton').forEach(el => {
                  el.classList.remove('loading', 'skeleton');
                });

                // Force redraw of admin panels
                const adminPanels = document.querySelectorAll('.flex-1.w-full, .max-w-full');
                if (adminPanels.length > 0) {
                  adminPanels.forEach(panel => {
                    // Trigger reflow
                    void panel.offsetHeight;
                  });
                }

                // Reset any loading state variables in React components
                // This uses a custom event that components can listen for
                const resetEvent = new CustomEvent('reset-loading-states');
                document.dispatchEvent(resetEvent);

                // Force any stuck loading spinners to hide after a delay
                setTimeout(() => {
                  document.querySelectorAll('[role="progressbar"], .animate-spin').forEach(spinner => {
                    const parent = spinner.parentElement;
                    if (parent && !parent.classList.contains('keep-spinner')) {
                      parent.style.display = 'none';
                    }
                  });
                }, 15000); // 15 second absolute maximum for any spinner
              }

              // Reset on initial page load
              window.addEventListener('load', resetAllLoadingStates);

              // Also reset after any route change completes
              document.addEventListener('routeChangeComplete', resetAllLoadingStates);

              // Listen for popstate events (browser back/forward)
              window.addEventListener('popstate', resetAllLoadingStates);

              // Set multiple fallback timeouts to ensure loading states don't get stuck
              setTimeout(resetAllLoadingStates, 3000);  // Quick reset
              setTimeout(resetAllLoadingStates, 8000);  // Medium reset
              setTimeout(resetAllLoadingStates, 15000); // Final failsafe

              // Fix for Next.js static asset 404 errors
              window.addEventListener('error', function(e) {
                const target = e.target;
                if (target && target.tagName === 'SCRIPT' && target.src && target.src.includes('_next/static')) {
                  console.warn('Attempting to reload failed script:', target.src);

                  // Create a new script element
                  const newScript = document.createElement('script');
                  newScript.src = target.src.includes('?')
                    ? target.src + '&retry=1'
                    : target.src + '?retry=1';
                  newScript.async = true;

                  // Replace the failed script
                  target.parentNode.replaceChild(newScript, target);

                  // Prevent the default error behavior
                  e.preventDefault();
                }
              }, true);
            }
          `}
        </Script>

        <TempoInit />

        <Providers>
          <ThemeInitializer />
          <MetaHead />
          {/* Loading indicator for page transitions */}
          <LoadingIndicator />
          {/* Top loading progress bar */}
          <NavigationProgress />
          {children}
        </Providers>
      </body>
    </html>
  );
}


