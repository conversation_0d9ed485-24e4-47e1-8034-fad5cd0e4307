'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, Plus, X, Tag } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Category {
  _id: string;
  name: string;
  slug: string;
  count?: number;
}

interface CategoryDropdownProps {
  selectedCategories: string[];
  onCategoriesChange: (categories: string[]) => void;
  placeholder?: string;
  className?: string;
}

export function CategoryDropdown({
  selectedCategories = [],
  onCategoriesChange,
  placeholder = "Select categories",
  className = "",
}: CategoryDropdownProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  // Fetch categories
  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/categories');
      const data = await response.json();
      
      if (data.success) {
        setCategories(data.data || []);
      } else {
        console.error('Failed to fetch categories:', data.error);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  };

  // Create new category
  const createCategory = async () => {
    if (!newCategoryName.trim()) {
      toast({
        title: 'Error',
        description: 'Category name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsCreating(true);
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newCategoryName.trim() }),
      });

      const data = await response.json();

      if (data.success) {
        const newCategory = data.data;
        setCategories(prev => [...prev, newCategory]);
        
        // Auto-select the new category
        const updatedCategories = [...selectedCategories, newCategory._id];
        onCategoriesChange(updatedCategories);
        
        setNewCategoryName('');
        toast({
          title: 'Success',
          description: `Category "${newCategory.name}" created and selected`,
        });
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to create category',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error creating category:', error);
      toast({
        title: 'Error',
        description: 'Failed to create category',
        variant: 'destructive',
      });
    } finally {
      setIsCreating(false);
    }
  };

  // Toggle category selection
  const toggleCategory = (categoryId: string) => {
    const updatedCategories = selectedCategories.includes(categoryId)
      ? selectedCategories.filter(id => id !== categoryId)
      : [...selectedCategories, categoryId];
    
    onCategoriesChange(updatedCategories);
  };

  // Remove category from selection
  const removeCategory = (categoryId: string) => {
    const updatedCategories = selectedCategories.filter(id => id !== categoryId);
    onCategoriesChange(updatedCategories);
  };

  // Get selected category names
  const getSelectedCategoryNames = () => {
    return categories
      .filter(cat => selectedCategories.includes(cat._id))
      .map(cat => cat.name);
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Category Dropdown */}
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-between"
            type="button"
          >
            <div className="flex items-center gap-2">
              <Tag className="h-4 w-4" />
              {selectedCategories.length > 0
                ? `${selectedCategories.length} categor${selectedCategories.length === 1 ? 'y' : 'ies'} selected`
                : placeholder}
            </div>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent className="w-80" align="start">
          <DropdownMenuLabel>Select Categories</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {/* Existing Categories */}
          {loading ? (
            <div className="p-2 text-center text-sm text-muted-foreground">
              Loading categories...
            </div>
          ) : categories.length > 0 ? (
            categories.map((category) => (
              <DropdownMenuItem
                key={category._id}
                onClick={() => toggleCategory(category._id)}
                className="flex items-center justify-between cursor-pointer"
              >
                <span>{category.name}</span>
                {selectedCategories.includes(category._id) && (
                  <div className="h-2 w-2 rounded-full bg-primary" />
                )}
              </DropdownMenuItem>
            ))
          ) : (
            <div className="p-2 text-center text-sm text-muted-foreground">
              No categories found
            </div>
          )}
          
          <DropdownMenuSeparator />
          
          {/* Create New Category */}
          <div className="p-2 space-y-2">
            <div className="flex gap-2">
              <Input
                placeholder="New category name"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    createCategory();
                  }
                }}
                className="flex-1"
              />
              <Button
                size="sm"
                onClick={createCategory}
                disabled={isCreating || !newCategoryName.trim()}
                type="button"
              >
                {isCreating ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                ) : (
                  <Plus className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Selected Categories Display */}
      {selectedCategories.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {getSelectedCategoryNames().map((categoryName, index) => {
            const categoryId = selectedCategories[index];
            return (
              <Badge key={categoryId} variant="secondary" className="flex items-center gap-1">
                {categoryName}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                  onClick={() => removeCategory(categoryId)}
                />
              </Badge>
            );
          })}
        </div>
      )}
    </div>
  );
}
