import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";
import User from "@/models/User";

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const category = searchParams.get("category");
    const search = searchParams.get("search");
    const isAdmin = searchParams.get("admin") === "true";

    // Build query
    const query: any = {};

    // For public pages, only show published posts
    if (!isAdmin) {
      query.status = "published";
      query.visibility = "public";
    } else if (status) {
      // For admin, filter by status if provided
      query.status = status;
    }

    // Filter by category
    if (category) {
      query.categories = { $in: [category] };
    }

    // Search functionality
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: "i" } },
        { content: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    console.log("Posts API query:", JSON.stringify(query, null, 2));

    // Execute query
    const [posts, total] = await Promise.all([
      BlogPost.find(query)
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .lean(),
      BlogPost.countDocuments(query),
    ]);

    console.log(`Posts API: Found ${posts.length} posts out of ${total} total`);

    // Format posts for frontend
    const formattedPosts = posts.map(post => ({
      id: post._id.toString(),
      _id: post._id.toString(),
      title: post.title,
      content: post.content,
      slug: post.slug,
      description: post.description || "",
      excerpt: post.description || post.content?.substring(0, 150) + "..." || "",
      status: post.status,
      visibility: post.visibility || "public",
      featuredImage: post.featuredImage || "",
      imageCredit: post.imageCredit || "",
      tags: post.tags || [],
      categories: post.categories || [],
      publishedAt: post.publishedAt || post.createdAt,
      createdAt: post.createdAt,
      updatedAt: post.updatedAt,
      scheduledAt: post.scheduledAt,
      author: {
        name: "Admin User",
        email: "<EMAIL>"
      }
    }));

    return NextResponse.json({
      success: true,
      data: formattedPosts,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    });

  } catch (error) {
    console.error("GET /api/posts error:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: "Failed to fetch posts", 
        data: [],
        pagination: { page: 1, limit: 10, total: 0, totalPages: 0, hasNext: false, hasPrev: false }
      },
      { status: 500 }
    );
  }
}

// Create new post
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();

    const body = await request.json();
    console.log("Creating post with data:", JSON.stringify(body, null, 2));

    // Get user ID from headers or use default
    const userId = request.headers.get("x-user-id") || "507f1f77bcf86cd799439011";

    // Auto-generate slug if not provided
    const slug = body.slug || body.title
      .toLowerCase()
      .replace(/[^\w\s]/gi, "")
      .replace(/\s+/g, "-")
      .substring(0, 50);

    // Check if slug exists
    const existingPost = await BlogPost.findOne({ slug });
    if (existingPost) {
      return NextResponse.json(
        { success: false, error: "A post with this slug already exists" },
        { status: 409 }
      );
    }

    // Create the post
    const postData = {
      title: body.title,
      content: body.content,
      slug,
      description: body.description || body.excerpt || "",
      status: body.status || "draft",
      visibility: body.visibility || "public",
      featuredImage: body.featuredImage || body.coverImage || "",
      imageCredit: body.imageCredit || "",
      tags: body.tags || [],
      categories: body.categories || [],
      authorId: userId,
      publishedAt: body.status === "published" ? new Date() : null,
      scheduledAt: body.scheduledAt ? new Date(body.scheduledAt) : null,
    };

    const newPost = await BlogPost.create(postData);

    console.log("Post created successfully:", newPost._id);

    return NextResponse.json({
      success: true,
      data: {
        id: newPost._id.toString(),
        _id: newPost._id.toString(),
        ...newPost.toObject(),
      }
    }, { status: 201 });

  } catch (error) {
    console.error("POST /api/posts error:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create post" },
      { status: 500 }
    );
  }
}
