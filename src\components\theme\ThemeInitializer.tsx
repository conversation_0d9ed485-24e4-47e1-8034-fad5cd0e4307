"use client";

import { useEffect } from 'react';
import { useAppDispatch } from '@/redux/hooks';
import { setTheme, Theme } from '@/redux/slices/themeSlice';

export function ThemeInitializer() {
  const dispatch = useAppDispatch();

  // Initialize theme from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedTheme = localStorage.getItem('theme') as Theme | null;
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        
        // Apply theme to document
        const theme = savedTheme || systemTheme;
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add(theme);
        
        dispatch(setTheme(theme));
      } catch (error) {
        console.error('Error initializing theme:', error);
      }
    }
  }, [dispatch]);

  // This component doesn't render anything
  return null;
}

